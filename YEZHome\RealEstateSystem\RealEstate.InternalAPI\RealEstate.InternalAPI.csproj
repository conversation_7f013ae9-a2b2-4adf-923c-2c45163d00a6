<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9bba9351-9b35-4e7e-b574-7488137a205a</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="CustomAuthorizationPolicy\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RealEstate.Application\RealEstate.Application.csproj" />
    <ProjectReference Include="..\RealEstate.Domain\RealEstate.Domain.csproj" />
  </ItemGroup>

</Project>
