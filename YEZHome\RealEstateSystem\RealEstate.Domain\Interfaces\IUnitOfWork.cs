﻿using RealEstate.Domain.Entities;

namespace RealEstate.Domain.Interfaces
{
    public interface IUnitOfWork
    {
        /*
         To add entity-specific functions like GetBlogPostByAuthor and GetBlogPostByDate for the BlogPost entity, you should:
            1. Create a specific repository interface for BlogPost.
            2. Implement the repository with custom methods.
            3. Access it through the Unit of Work.
         */

        IUserRepository AppUsers { get; }
        IBlogRepository BlogPosts { get; }
        IRepository<UserRole> UserRoles { get; }
        IRepository<PropertyMedia> PropertyMedias { get; }
        IRepository<UserAvatar> UserAvatars { get; }
        IRepository<PropertyStatusLog> PropertyStatusLogs { get; }
        IRepository<UserFavorite> UserFavorites { get; }
        IAuditableRepository<BlogComment> BlogComments { get; }
        IAuditableRepository<OwnerReview> OwnerReviews { get; }
        IAuditableRepository<PropertyReview> PropertyReviews { get; }
        IAuditableRepository<Property> Properties { get; }

        IAuditableRepository<ContactRequest> ContactRequests { get; }
        IRepository<Notification> Notifications { get; }
        IRepository<NotificationPreference> NotificationPreferences { get; }

        // Permission System
        IRepository<Permission> Permissions { get; }
        IRepository<RolePermission> RolePermissions { get; }
        IRepository<AdminRole> AdminRoles { get; }

        IRepository<Wallet> Wallets { get; }
        IRepository<WalletTransaction> WalletTransactions { get; }
        IRepository<MemberRanking> MemberRankings { get; }
        IRepository<HighlightFee> HighlightFees { get; }

        // Property Analytics
        IAuditableRepository<PropertyViewLog> PropertyViewLogs { get; }
        IAuditableRepository<PropertySpendingLog> PropertySpendingLogs { get; }
        IAuditableRepository<PropertyEngagementSummary> PropertyEngagementSummaries { get; }

        Task<int> SaveChangesAsync();
    }
}
