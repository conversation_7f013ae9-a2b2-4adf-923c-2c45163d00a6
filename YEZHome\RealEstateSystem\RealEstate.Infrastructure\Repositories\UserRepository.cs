﻿using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Infrastructure.Repositories
{
    public class UserRepository : AuditableRepository<AppUser>, IUserRepository
    {
        public UserRepository(ApplicationDbContext context) : base(context) { }

        public async Task<AppUser?> GetByEmailAsync(string email, bool asNoTracking = true, bool isIncludeRole = false)
        {
            var query = _dbSet.AsQueryable();

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            if (isIncludeRole)
            {
                query = query.Include(x => x.UserRoles)
                            .ThenInclude(ur => ur.Role)
                            .ThenInclude(r => r.RolePermissions)
                            .ThenInclude(rp => rp.Permission);
            }

            return await query.FirstOrDefaultAsync(x => x.Email == email);
        }

        public async Task<bool> EmailExistsAsync(string email)
        {
            return await _dbSet.AsNoTracking().AnyAsync(x => x.Email == email);
        }
    }
}
