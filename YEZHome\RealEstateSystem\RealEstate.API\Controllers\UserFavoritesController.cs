using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserFavoritesController : BaseController
    {
        private readonly IUserFavoriteService _favoriteService;
        private readonly ILogger<UserFavoritesController> _logger;

        public UserFavoritesController(IUserFavoriteService favoriteService, ILogger<UserFavoritesController> logger)
        {
            _favoriteService = favoriteService;
            _logger = logger;
        }

        [HttpGet("favorites")]
        public async Task<ActionResult<IEnumerable<UserFavoriteDto>>> GetUserFavorites()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedFavoritesAccess", "User attempted to access favorites without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogInformation("Retrieving favorites for user {UserId}", userId.Value);
                LogUserAction(_logger, "GetUserFavorites");

                var favorites = await _favoriteService.GetUserFavoritesAsync(userId.Value);

                _logger.LogInformation("Successfully retrieved {Count} favorites for user {UserId}", favorites?.Count() ?? 0, userId.Value);
                return Ok(favorites);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving favorites for user {UserId}", GetUserId());
                return StatusCode(500, new { Message = "An error occurred while retrieving favorites. Please try again later." });
            }
        }

        [HttpPost("add")]
        public async Task<IActionResult> AddToFavorites([FromBody] CreateUserFavoriteDto request)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedFavoriteAdd", "User attempted to add favorite without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogInformation("Adding property {PropertyId} to favorites for user {UserId}", request.PropertyId, userId.Value);
                LogUserAction(_logger, "AddToFavorites", new { PropertyId = request.PropertyId });

                var result = await _favoriteService.AddToFavoritesAsync(userId.Value, request.PropertyId);

                if (result)
                {
                    _logger.LogInformation("Successfully added property {PropertyId} to favorites for user {UserId}", request.PropertyId, userId.Value);
                }
                else
                {
                    _logger.LogWarning("Failed to add property {PropertyId} to favorites for user {UserId}", request.PropertyId, userId.Value);
                }

                return Ok(new { success = result });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Property {PropertyId} not found when adding to favorites for user {UserId}: {Message}",
                    request.PropertyId, GetUserId(), ex.Message);
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding property {PropertyId} to favorites for user {UserId}", request.PropertyId, GetUserId());
                return StatusCode(500, new { Message = "An error occurred while adding to favorites. Please try again later." });
            }
        }

        [HttpDelete("remove/{propertyId}")]
        public async Task<IActionResult> RemoveFromFavorites(Guid propertyId)
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            try
            {
                var result = await _favoriteService.RemoveFromFavoritesAsync(userId.Value, propertyId);
                return Ok(new { success = result });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("check")]
        public async Task<ActionResult<List<FavoriteStatusDto>>> CheckFavoriteStatus([FromBody] FavoriteCheckRequestDto request)
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            try
            {
                var result = await _favoriteService.CheckFavoriteStatusAsync(userId.Value, request.PropertyIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("count")]
        public async Task<ActionResult<FavoriteCountDto>> GetFavoritesCount()
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            try
            {
                var count = await _favoriteService.GetFavoritesCountAsync(userId.Value);
                return Ok(new FavoriteCountDto { Count = count });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
} 