using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WalletTransactionController : BaseController
    {
        private readonly IWalletService _walletService;
        private readonly ILogger<WalletTransactionController> _logger;

        public WalletTransactionController(
            IWalletService walletService,
            ILogger<WalletTransactionController> logger)
        {
            _walletService = walletService;
            _logger = logger;
        }

        [HttpGet]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<IEnumerable<WalletTransactionDto>>> GetTransactions(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var transactions = await _walletService.GetWalletTransactionsAsync(userId.Value, page, pageSize);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving wallet transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("balance")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<WalletBalanceDto>> GetBalance()
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var balance = await _walletService.GetWalletBalanceAsync(userId.Value);
                return Ok(balance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving wallet balance");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("topup")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<WalletTransactionDto>> TopUpWallet([FromBody] TopUpWalletDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var transaction = await _walletService.TopUpWalletAsync(userId.Value, request);
                return Ok(transaction);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing wallet top-up");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("spend")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<WalletTransactionDto>> SpendFromWallet([FromBody] SpendWalletDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var transaction = await _walletService.SpendFromWalletAsync(userId.Value, request);
                return Ok(transaction);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing wallet spend");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<WalletTransactionDto>> GetTransactionById(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var transaction = await _walletService.GetTransactionByIdAsync(id, userId.Value);
                if (transaction == null)
                {
                    return NotFound();
                }

                return Ok(transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving transaction");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("pending")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<IEnumerable<WalletTransactionDto>>> GetUserPendingTransactions(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var transactions = await _walletService.GetUserPendingTransactionsAsync(userId.Value, page, pageSize);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pending transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        } 

        [HttpGet("search")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<TransactionSearchResultDto>> SearchTransactions(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentMethod = null,
            [FromQuery] string? type = null,
            [FromQuery] decimal? minAmount = null,
            [FromQuery] decimal? maxAmount = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var criteria = new TransactionSearchCriteriaDto
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    Status = status,
                    PaymentMethod = paymentMethod,
                    Type = type,
                    MinAmount = minAmount,
                    MaxAmount = maxAmount,
                    SearchTerm = searchTerm,
                    Page = page,
                    PageSize = pageSize
                };

                var result = await _walletService.SearchTransactionsAsync(criteria, userId.Value);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("export")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> ExportTransactions(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentMethod = null,
            [FromQuery] string? type = null,
            [FromQuery] decimal? minAmount = null,
            [FromQuery] decimal? maxAmount = null,
            [FromQuery] string? searchTerm = null)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return Unauthorized("Invalid user");
                }

                var criteria = new TransactionSearchCriteriaDto
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    Status = status,
                    PaymentMethod = paymentMethod,
                    Type = type,
                    MinAmount = minAmount,
                    MaxAmount = maxAmount,
                    SearchTerm = searchTerm
                };

                var excelData = await _walletService.ExportTransactionsToExcelAsync(criteria, userId.Value);

                // Generate a filename with current date
                string fileName = $"Transactions_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
