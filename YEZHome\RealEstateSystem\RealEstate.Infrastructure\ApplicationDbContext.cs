﻿using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Infrastructure
{
    public class ApplicationDbContext : DbContext
    {
        public DbSet<City> City { get; set; }
        public DbSet<District> District { get; set; }
        public DbSet<Ward> Ward { get; set; }
        public DbSet<Street> Street { get; set; }
        public DbSet<Project> Project { get; set; }
        public DbSet<Wallet> Wallets { get; set; }
        public DbSet<WalletTransaction> WalletTransactions { get; set; }
        public DbSet<MemberRanking> MemberRankings { get; set; }
        public DbSet<UserAvatar> UserAvatar { get; set; }
        public DbSet<NotificationPreference> NotificationPreferences { get; set; }

        // Permission System
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }

        // Property Analytics
        public DbSet<PropertyViewLog> PropertyViewLogs { get; set; }
        public DbSet<PropertySpendingLog> PropertySpendingLogs { get; set; }
        public DbSet<PropertyEngagementSummary> PropertyEngagementSummaries { get; set; }
        public DbSet<HighlightFee> HighlightFees { get; set; }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<BlogComment>().HasQueryFilter(c => !c.IsDeleted);
            modelBuilder.Entity<BlogPost>().HasQueryFilter(c => !c.IsDeleted);
            modelBuilder.Entity<OwnerReview>().HasQueryFilter(c => !c.IsDeleted);
            modelBuilder.Entity<Property>().HasQueryFilter(c => !c.IsDeleted);
            modelBuilder.Entity<PropertyReview>().HasQueryFilter(c => !c.IsDeleted);
            modelBuilder.Entity<AppUser>().HasQueryFilter(c => !c.IsDeleted);
            modelBuilder.Entity<ContactRequest>().HasQueryFilter(c => !c.IsDeleted);

            modelBuilder.Entity<AppUser>()
                .Property(p => p.CreatedAt)
                .HasConversion
                (
                    src => src.Kind == DateTimeKind.Utc ? src : DateTime.SpecifyKind(src, DateTimeKind.Utc),
                    dst => dst.Kind == DateTimeKind.Utc ? dst : DateTime.SpecifyKind(dst, DateTimeKind.Utc)
                );

            modelBuilder.Entity<AppUser>()
                .Property(p => p.UpdatedAt)
                .HasConversion
                (
                    src => src.HasValue ? (src.Value.Kind == DateTimeKind.Utc ? src.Value : DateTime.SpecifyKind(src.Value, DateTimeKind.Utc)) : src,
                    dst => dst.HasValue ? (dst.Value.Kind == DateTimeKind.Utc ? dst.Value : DateTime.SpecifyKind(dst.Value, DateTimeKind.Utc)) : dst
                );

            modelBuilder.Entity<AppUser>()
                .Property(p => p.DeletedAt)
                .HasConversion
                (
                    src => src.HasValue ? (src.Value.Kind == DateTimeKind.Utc ? src.Value : DateTime.SpecifyKind(src.Value, DateTimeKind.Utc)) : src,
                    dst => dst.HasValue ? (dst.Value.Kind == DateTimeKind.Utc ? dst.Value : DateTime.SpecifyKind(dst.Value, DateTimeKind.Utc)) : dst
                );

            modelBuilder.Entity<AppUser>()
                .Property(p => p.LastLogin)
                .HasConversion
                (
                    src => src.HasValue ? (src.Value.Kind == DateTimeKind.Utc ? src.Value : DateTime.SpecifyKind(src.Value, DateTimeKind.Utc)) : src,
                    dst => dst.HasValue ? (dst.Value.Kind == DateTimeKind.Utc ? dst.Value : DateTime.SpecifyKind(dst.Value, DateTimeKind.Utc)) : dst
                );

            modelBuilder.Entity<UserRole>()
                .HasQueryFilter(u => !u.User.IsDeleted);

            // Permission System relationships
            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionID)
                .OnDelete(DeleteBehavior.Cascade);

            // Ensure unique combination of RoleID and PermissionID
            modelBuilder.Entity<RolePermission>()
                .HasIndex(rp => new { rp.RoleID, rp.PermissionID })
                .IsUnique();

            // Define relationships
            modelBuilder.Entity<BlogComment>()
                .HasOne(bc => bc.Post)
                .WithMany(bp => bp.BlogComments)
                .HasForeignKey(bc => bc.PostID);

            modelBuilder.Entity<OwnerReview>()
                .HasOne(or => or.Owner)
                .WithMany(o => o.OwnerReviews)
                .HasForeignKey(or => or.OwnerID);

            modelBuilder.Entity<PropertyReview>()
                .HasOne(pr => pr.Property)
                .WithMany(p => p.PropertyReviews)
                .HasForeignKey(pr => pr.PropertyID);

            modelBuilder.Entity<UserFavorite>()
                .HasOne(uf => uf.User)
                .WithMany(u => u.UserFavorites)
                .HasForeignKey(uf => uf.UserID);

            modelBuilder.Entity<UserFavorite>()
                .HasOne(uf => uf.Property)
                .WithMany(p => p.UserFavorites)
                .HasForeignKey(uf => uf.PropertyID);

            modelBuilder.Entity<UserFavorite>()
                .HasQueryFilter(uf => uf.Property != null && !uf.Property.IsDeleted);

            // Location Tables
            modelBuilder.Entity<City>().HasKey(e => e.Id);

            modelBuilder.Entity<District>().HasOne(d => d.City)
                      .WithMany(c => c.Districts)
                      .HasForeignKey(d => d.CityId);

            modelBuilder.Entity<Ward>().HasOne(w => w.District)
                      .WithMany(d => d.Wards)
                      .HasForeignKey(w => w.DistrictId);

            modelBuilder.Entity<Street>().HasOne(s => s.District)
                      .WithMany(d => d.Streets)
                      .HasForeignKey(s => s.DistrictId);

            // Project Table
            modelBuilder.Entity<Project>(entity =>
            {
                entity.HasOne(p => p.Ward)
                      .WithMany(w => w.Projects)
                      .HasForeignKey(p => p.WardId);
                entity.HasOne(p => p.Street)
                      .WithMany(s => s.Projects)
                      .HasForeignKey(p => p.StreetId);
            });

            // ContactRequest table
            modelBuilder.Entity<ContactRequest>(entity =>
            {
                entity.HasOne(e => e.Property)
                     .WithMany()
                     .HasForeignKey(e => e.PropertyId)
                     .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Agent)
                    .WithMany()
                    .HasForeignKey(e => e.AgentId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            modelBuilder.Entity<Notification>().HasOne(d => d.User)
                    .WithMany()
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

            // Property Analytics relationships
            modelBuilder.Entity<PropertyViewLog>().HasOne(p => p.Property)
                    .WithMany()
                    .HasForeignKey(p => p.PropertyId)
                    .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PropertyViewLog>().HasOne(p => p.Viewer)
                    .WithMany()
                    .HasForeignKey(p => p.ViewerId);

            modelBuilder.Entity<PropertySpendingLog>().HasOne(p => p.Property)
                    .WithMany()
                    .HasForeignKey(p => p.PropertyId)
                    .IsRequired(false)
                    .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PropertySpendingLog>().HasOne(p => p.User)
                    .WithMany()
                    .HasForeignKey(p => p.UserId)
                    .IsRequired(false);

            modelBuilder.Entity<PropertyEngagementSummary>().HasOne(p => p.Property)
                    .WithOne()
                    .HasForeignKey<PropertyEngagementSummary>(p => p.PropertyId)
                    .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PropertyEngagementSummary>()
                    .HasQueryFilter(p => p.Property != null && !p.Property.IsDeleted);

            // Configure the one-to-one relationship between MemberRanking and HighlightFee
            modelBuilder.Entity<HighlightFee>()
                    .HasOne(hf => hf.MemberRanking)
                    .WithOne(mr => mr.HighlightFee)
                    .HasForeignKey<HighlightFee>(hf => hf.RankName)
                    .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<NotificationPreference>()
                    .HasQueryFilter(p => !p.User.IsDeleted);

            modelBuilder.Entity<PropertyStatusLog>()
                    .HasQueryFilter(p => p.Property != null && !p.Property.IsDeleted);

            modelBuilder.Entity<PropertyStatusLog>()
                    .HasQueryFilter(p => p.ChangedByUser != null && !p.ChangedByUser.IsDeleted);

            modelBuilder.Entity<PropertyViewLog>()
                    .HasQueryFilter(p => p.Property != null && !p.Property.IsDeleted);

            modelBuilder.Entity<PropertyViewLog>()
                    .HasQueryFilter(p => p.Viewer != null && !p.Viewer.IsDeleted);

            modelBuilder.Entity<UserAvatar>()
                    .HasQueryFilter(p => p.User != null && !p.User.IsDeleted);

            modelBuilder.Entity<Wallet>()
                    .HasQueryFilter(p => p.User != null && !p.User.IsDeleted);

            modelBuilder.Entity<WalletTransaction>()
                    .HasQueryFilter(p => p.User != null && !p.User.IsDeleted);
        }
    }
}
