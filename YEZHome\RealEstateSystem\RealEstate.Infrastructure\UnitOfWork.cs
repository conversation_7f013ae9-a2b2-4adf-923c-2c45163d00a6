﻿using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using RealEstate.Infrastructure.Repositories;

namespace RealEstate.Infrastructure
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private readonly ApplicationDbContext _context;
        private bool _disposed = false;

        public IUserRepository AppUsers { get; }
        public IBlogRepository BlogPosts { get; }
        public IRepository<UserRole> UserRoles { get; }
        public IRepository<PropertyMedia> PropertyMedias { get; }
        public IRepository<UserAvatar> UserAvatars { get; }
        public IRepository<PropertyStatusLog> PropertyStatusLogs { get; }
        public IRepository<UserFavorite> UserFavorites { get; }
        public IAuditableRepository<BlogComment> BlogComments { get; }
        public IAuditableRepository<OwnerReview> OwnerReviews { get; }
        public IAuditableRepository<PropertyReview> PropertyReviews { get; }
        public IAuditableRepository<Property> Properties { get; }
        public IAuditableRepository<ContactRequest> ContactRequests { get; }
        public IRepository<Notification> Notifications { get; }
        public IRepository<NotificationPreference> NotificationPreferences { get; }

        // Permission System
        public IRepository<Permission> Permissions { get; }
        public IRepository<RolePermission> RolePermissions { get; }
        public IRepository<AdminRole> AdminRoles { get; }

        public IRepository<Wallet> Wallets { get; }
        public IRepository<WalletTransaction> WalletTransactions { get; }
        public IRepository<MemberRanking> MemberRankings { get; }
        public IRepository<HighlightFee> HighlightFees { get; }

        // Property Analytics
        public IAuditableRepository<PropertyViewLog> PropertyViewLogs { get; }
        public IAuditableRepository<PropertySpendingLog> PropertySpendingLogs { get; }
        public IAuditableRepository<PropertyEngagementSummary> PropertyEngagementSummaries { get; }

        public UnitOfWork(ApplicationDbContext context)
        {
            _context = context;
            AppUsers = new UserRepository(_context);
            BlogPosts = new BlogRepository(_context);
            UserRoles = new Repository<UserRole>(_context);
            PropertyMedias = new Repository<PropertyMedia>(_context);
            UserAvatars = new Repository<UserAvatar>(_context);
            PropertyStatusLogs = new Repository<PropertyStatusLog>(_context);
            UserFavorites = new Repository<UserFavorite>(_context);
            BlogComments = new AuditableRepository<BlogComment>(_context);
            OwnerReviews = new AuditableRepository<OwnerReview>(_context);
            PropertyReviews = new AuditableRepository<PropertyReview>(_context);
            Properties = new AuditableRepository<Property>(_context);
            ContactRequests = new AuditableRepository<ContactRequest>(_context);
            Notifications = new Repository<Notification>(_context);
            NotificationPreferences = new Repository<NotificationPreference>(_context);

            // Permission System
            Permissions = new Repository<Permission>(_context);
            RolePermissions = new Repository<RolePermission>(_context);
            AdminRoles = new Repository<AdminRole>(_context);

            Wallets = new Repository<Wallet>(_context);
            WalletTransactions = new Repository<WalletTransaction>(_context);
            MemberRankings = new Repository<MemberRanking>(_context);
            HighlightFees = new Repository<HighlightFee>(_context);

            // Property Analytics
            PropertyViewLogs = new AuditableRepository<PropertyViewLog>(_context);
            PropertySpendingLogs = new AuditableRepository<PropertySpendingLog>(_context);
            PropertyEngagementSummaries = new AuditableRepository<PropertyEngagementSummary>(_context);
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context.Dispose();
                }

                // Dispose unmanaged resources if any

                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
